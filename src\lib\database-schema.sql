-- Enable RLS (Row Level Security)
-- This should be run in your Supabase SQL editor

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'staff', 'volunteer', 'foster', 'user')),
  full_name TEXT,
  phone TEXT,
  address TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Animals table
CREATE TABLE IF NOT EXISTS animals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  species TEXT NOT NULL CHECK (species IN ('dog', 'cat', 'other')),
  breed TEXT,
  age TEXT,
  gender TEXT NOT NULL CHECK (gender IN ('male', 'female', 'unknown')),
  size TEXT NOT NULL CHECK (size IN ('small', 'medium', 'large')),
  status TEXT NOT NULL DEFAULT 'available' CHECK (status IN ('available', 'pending', 'adopted', 'fostered', 'medical_hold')),
  intake_date DATE NOT NULL,
  rescue_location TEXT,
  intake_story TEXT,
  medical_notes TEXT,
  behavioral_notes TEXT,
  photos TEXT[] DEFAULT '{}',
  videos TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Animal updates table
CREATE TABLE IF NOT EXISTS animal_updates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  update_type TEXT NOT NULL CHECK (update_type IN ('medical', 'behavioral', 'general', 'milestone')),
  content TEXT NOT NULL,
  author_name TEXT NOT NULL,
  author_role TEXT NOT NULL,
  media_urls TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Sponsorships table
CREATE TABLE IF NOT EXISTS sponsorships (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  sponsor_type TEXT NOT NULL CHECK (sponsor_type IN ('animal', 'meal', 'vaccine', 'surgery', 'toy')),
  amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  target_amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE animals ENABLE ROW LEVEL SECURITY;
ALTER TABLE animal_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE sponsorships ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users policies
CREATE POLICY "Users can view their own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Animals policies (public read access)
CREATE POLICY "Anyone can view animals" ON animals
  FOR SELECT USING (true);

CREATE POLICY "Staff can insert animals" ON animals
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can update animals" ON animals
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admins can delete animals" ON animals
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Animal updates policies
CREATE POLICY "Anyone can view animal updates" ON animal_updates
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create updates" ON animal_updates
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Users can update their own updates" ON animal_updates
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Staff can update any update" ON animal_updates
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'staff')
    )
  );

-- Sponsorships policies
CREATE POLICY "Anyone can view sponsorships" ON sponsorships
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create sponsorships" ON sponsorships
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Users can view their own sponsorships" ON sponsorships
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Staff can manage all sponsorships" ON sponsorships
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'staff')
    )
  );

-- Functions for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = TIMEZONE('utc'::text, NOW());
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_animals_updated_at
  BEFORE UPDATE ON animals
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Insert sample admin user (replace with your email)
-- This should be done after you've signed up through the app
-- UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';

-- Insert sample animals for testing
INSERT INTO animals (name, species, breed, age, gender, size, status, intake_date, rescue_location, intake_story) VALUES
('Luna', 'dog', 'Golden Retriever Mix', '2 years', 'female', 'large', 'available', '2024-01-15', 'Downtown Shelter', 'Luna was found wandering the streets, hungry but gentle. She loves kids and other dogs.'),
('Buddy', 'dog', 'Labrador Mix', '3 years', 'male', 'large', 'available', '2024-02-01', 'Rural Rescue', 'Buddy was surrendered when his family moved. He is house-trained and knows basic commands.'),
('Whiskers', 'cat', 'Domestic Shorthair', '1 year', 'male', 'small', 'available', '2024-01-20', 'Animal Control', 'Whiskers is a playful kitten who loves toys and sunny windowsills.');