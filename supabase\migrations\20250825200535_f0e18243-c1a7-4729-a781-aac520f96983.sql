-- Fix security vulnerability: Remove public access to individual sponsorship records
-- Remove the overly permissive policy that allows anyone to view all sponsorships
DROP POLICY IF EXISTS "Anyone can view sponsorships" ON sponsorships;

-- Create a secure view for public access to aggregated sponsorship data only
CREATE OR REPLACE VIEW public.animal_sponsorship_totals AS
SELECT 
  animal_id,
  SUM(amount) as total_raised,
  COUNT(*) as sponsor_count
FROM sponsorships 
WHERE status = 'active'
GROUP BY animal_id;

-- Grant public access to the aggregated view only
GRANT SELECT ON public.animal_sponsorship_totals TO anon;
GRANT SELECT ON public.animal_sponsorship_totals TO authenticated;

-- Ensure existing policies remain for secure access:
-- 1. Users can create their own sponsorships (already exists)
-- 2. Users can update their own sponsorships (already exists) 
-- 3. Staff can manage all sponsorships (already exists)

-- Add policy to allow users to view their own sponsorships (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'sponsorships' 
    AND policyname = 'Users can view their own sponsorships'
  ) THEN
    CREATE POLICY "Users can view their own sponsorships" ON sponsorships
      FOR SELECT USING (auth.uid() = user_id);
  END IF;
END $$;