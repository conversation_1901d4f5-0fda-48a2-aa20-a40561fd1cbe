@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animal Rescue CRM Design System - Warm, compassionate colors inspired by nature */

@layer base {
  :root {
    --primary-color: #1c64c0;

    /* Base colors - warm, inviting background */
    --background: 45 25% 98%;
    --foreground: 15 15% 25%;

    /* Card system - soft, elevated feel */
    --card: 0 0% 100%;
    --card-foreground: 15 15% 25%;

    --popover: 0 0% 100%;
    --popover-foreground: 15 15% 25%;

    /* Primary - warm rescue orange */
    --primary: 25 85% 55%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 25 85% 45%;

    /* Secondary - gentle nature green */
    --secondary: 145 30% 85%;
    --secondary-foreground: 145 40% 25%;

    /* Muted - soft neutrals */
    --muted: 45 15% 92%;
    --muted-foreground: 15 10% 55%;

    /* Accent - caring blue */
    --accent: 200 50% 88%;
    --accent-foreground: 200 60% 25%;

    /* Destructive - gentle warning */
    --destructive: 5 75% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Borders and inputs */
    --border: 45 20% 88%;
    --input: 45 20% 88%;
    --ring: 25 85% 55%;

    --radius: 0.75rem;

    /* Rescue-specific design tokens */
    --success: 145 65% 45%;
    --success-foreground: 0 0% 98%;
    --warning: 45 85% 60%;
    --warning-foreground: 15 15% 25%;

    /* Beautiful gradients */
    --gradient-primary: linear-gradient(135deg, hsl(25 85% 55%), hsl(45 85% 60%));
    --gradient-secondary: linear-gradient(135deg, hsl(145 30% 85%), hsl(200 50% 88%));
    --gradient-hero: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color) 100%);

    /* Elegant shadows */
    --shadow-soft: 0 2px 8px -2px hsl(25 85% 55% / 0.1);
    --shadow-medium: 0 8px 25px -5px hsl(25 85% 55% / 0.15);
    --shadow-strong: 0 20px 40px -10px hsl(25 85% 55% / 0.2);

    /* Smooth transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Rescue-themed utility classes */
  .gradient-primary {
    background: var(--gradient-primary);
  }
  
  .gradient-secondary {
    background: var(--gradient-secondary);
  }
  
  .gradient-hero {
    background: var(--gradient-hero);
  }
  
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }
  
  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }
  
  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }
  
  .transition-smooth {
    transition: var(--transition-smooth);
  }
  
  .transition-bounce {
    transition: var(--transition-bounce);
  }
}