// Database types
export interface User {
  id: string
  email: string
  role: 'admin' | 'staff' | 'volunteer' | 'foster' | 'user'
  full_name?: string
  phone?: string
  address?: string
  created_at: string
  updated_at: string
}

export interface Animal {
  id: string
  name: string
  species: 'dog' | 'cat' | 'other'
  breed?: string
  age?: string
  gender: 'male' | 'female' | 'unknown'
  size: 'small' | 'medium' | 'large'
  status: 'available' | 'pending' | 'adopted' | 'fostered' | 'medical_hold'
  intake_date: string
  rescue_location?: string
  intake_story?: string
  medical_notes?: string
  behavioral_notes?: string
  photos: string[]
  videos: string[]
  created_at: string
  updated_at: string
}

export interface AnimalUpdate {
  id: string
  animal_id: string
  user_id: string
  update_type: 'medical' | 'behavioral' | 'general' | 'milestone'
  content: string
  author_name: string
  author_role: string
  media_urls: string[]
  created_at: string
}

export interface Sponsorship {
  id: string
  animal_id: string
  user_id: string
  sponsor_type: 'animal' | 'meal' | 'vaccine' | 'surgery' | 'toy'
  amount: number
  target_amount: number
  status: 'active' | 'completed' | 'cancelled'
  created_at: string
}