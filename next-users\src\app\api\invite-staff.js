// pages/api/invite-staff.js
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { email, full_name, phone, address, role } = req.body;

  // Create the user in Supabase Auth
  const { data, error } = await supabase.auth.admin.createUser({
    email,
    password: 'password', // Optional, defaults to random password
    email_confirm: false,
    invite: true, // Optional, defaults to false
    user_metadata: { full_name, phone, address, role }
  });
  await supabase.auth.admin.sendVerificationEmail(email);
  
  console.log('Supabase createUser response:', { data, error });
  
  if (error) {
    return res.status(400).json({ error: error.message });
  }

  // Optionally, insert into your users table
  await supabase.from('users').insert([{
    id: data.user.id,
    full_name,
    email,
    phone,
    address,
    role,
  }]);

  return res.status(200).json({ user: data.user });
}