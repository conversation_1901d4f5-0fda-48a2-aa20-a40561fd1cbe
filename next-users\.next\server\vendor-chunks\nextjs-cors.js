"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nextjs-cors";
exports.ids = ["vendor-chunks/nextjs-cors"];
exports.modules = {

/***/ "(rsc)/../node_modules/nextjs-cors/dist/nextjs-cors.esm.js":
/*!***********************************************************!*\
  !*** ../node_modules/nextjs-cors/dist/nextjs-cors.esm.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var cors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cors */ \"(rsc)/../node_modules/cors/lib/index.js\");\n/* harmony import */ var cors__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(cors__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// And to throw an error when an error happens in a middleware\n\nfunction initMiddleware(middleware) {\n  return function (req, res, options) {\n    return new Promise(function (resolve, reject) {\n      middleware(options)(req, res, function (result) {\n        if (result instanceof Error) {\n          return reject(result);\n        }\n\n        return resolve(result);\n      });\n    });\n  };\n} // You can read more about the available options here: https://github.com/expressjs/cors#configuration-options\n\n\nvar NextCors = /*#__PURE__*/initMiddleware((cors__WEBPACK_IMPORTED_MODULE_0___default()));\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NextCors);\n//# sourceMappingURL=nextjs-cors.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHRqcy1jb3JzL2Rpc3QvbmV4dGpzLWNvcnMuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3Qjs7QUFFeEI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0EsRUFBRTs7O0FBR0YsMkNBQTJDLDZDQUFJOztBQUUvQyxpRUFBZSxRQUFRLEVBQUM7QUFDeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbXVydGF6YVxcRG93bmxvYWRzXFxjb2RlXFxub2RlX21vZHVsZXNcXG5leHRqcy1jb3JzXFxkaXN0XFxuZXh0anMtY29ycy5lc20uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvcnMgZnJvbSAnY29ycyc7XG5cbi8vIEFuZCB0byB0aHJvdyBhbiBlcnJvciB3aGVuIGFuIGVycm9yIGhhcHBlbnMgaW4gYSBtaWRkbGV3YXJlXG5cbmZ1bmN0aW9uIGluaXRNaWRkbGV3YXJlKG1pZGRsZXdhcmUpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIChyZXEsIHJlcywgb3B0aW9ucykge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICBtaWRkbGV3YXJlKG9wdGlvbnMpKHJlcSwgcmVzLCBmdW5jdGlvbiAocmVzdWx0KSB7XG4gICAgICAgIGlmIChyZXN1bHQgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICAgIHJldHVybiByZWplY3QocmVzdWx0KTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiByZXNvbHZlKHJlc3VsdCk7XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfTtcbn0gLy8gWW91IGNhbiByZWFkIG1vcmUgYWJvdXQgdGhlIGF2YWlsYWJsZSBvcHRpb25zIGhlcmU6IGh0dHBzOi8vZ2l0aHViLmNvbS9leHByZXNzanMvY29ycyNjb25maWd1cmF0aW9uLW9wdGlvbnNcblxuXG52YXIgTmV4dENvcnMgPSAvKiNfX1BVUkVfXyovaW5pdE1pZGRsZXdhcmUoY29ycyk7XG5cbmV4cG9ydCBkZWZhdWx0IE5leHRDb3JzO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmV4dGpzLWNvcnMuZXNtLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/nextjs-cors/dist/nextjs-cors.esm.js\n");

/***/ })

};
;