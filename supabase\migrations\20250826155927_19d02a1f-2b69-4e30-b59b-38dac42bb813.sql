-- Fix security warnings by setting search_path for functions

-- Update functions to set search_path parameter
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT AS $$
  SELECT role FROM public.users WHERE id = auth.uid();
$$ LANGUAGE SQL SECURITY DEFINER STABLE SET search_path = public;

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
  SELECT CASE WHEN public.get_current_user_role() = 'admin' THEN true ELSE false END;
$$ LANGUAGE SQL SECURITY DEFINER STABLE SET search_path = public;

CREATE OR REPLACE FUNCTION public.is_staff_or_admin()
RETURNS BOOLEAN AS $$
  SELECT CASE WHEN public.get_current_user_role() IN ('admin', 'staff') THEN true ELSE false END;
$$ LANGUAGE SQL SECURITY DEFINER STABLE SET search_path = public;