-- Fix infinite recursion in RLS policies by creating security definer functions

-- Create security definer function to get current user role
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT AS $$
  SELECT role FROM public.users WHERE id = auth.uid();
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- Create security definer function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
  SELECT CASE WHEN public.get_current_user_role() = 'admin' THEN true ELSE false END;
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- Create security definer function to check if user is staff or admin
CREATE OR REPLACE FUNCTION public.is_staff_or_admin()
RETURNS BOOLEAN AS $$
  SELECT CASE WHEN public.get_current_user_role() IN ('admin', 'staff') THEN true ELSE false END;
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- Drop existing policies that cause recursion
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Recreate policies using security definer functions
CREATE POLICY "Users can view their own profile" 
ON public.users 
FOR SELECT 
USING (auth.uid() = id OR public.is_staff_or_admin());

CREATE POLICY "Admins can manage all users" 
ON public.users 
FOR ALL 
USING (public.is_admin());

-- Update other policies to use security definer functions
DROP POLICY IF EXISTS "Staff can manage animals" ON public.animals;
CREATE POLICY "Staff can manage animals" 
ON public.animals 
FOR ALL 
USING (public.is_staff_or_admin());

DROP POLICY IF EXISTS "Staff can manage all updates" ON public.animal_updates;
CREATE POLICY "Staff can manage all updates" 
ON public.animal_updates 
FOR ALL 
USING (public.is_staff_or_admin());

DROP POLICY IF EXISTS "Staff can manage all sponsorships" ON public.sponsorships;
CREATE POLICY "Staff can manage all sponsorships" 
ON public.sponsorships 
FOR ALL 
USING (public.is_staff_or_admin());