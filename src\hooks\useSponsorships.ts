import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

interface Sponsorship {
  id: string;
  animal_id: string;
  user_id: string;
  sponsor_type: string;
  amount: number;
  target_amount: number;
  status: string;
  created_at: string;
}

export function useSponsorships(animalId: string) {
  const [sponsorships, setSponsorships] = useState<Sponsorship[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalRaised, setTotalRaised] = useState(0);

  const fetchSponsorships = async () => {
    try {
      setLoading(true);
      
      // Only fetch if animalId is a valid UUID
      if (!animalId || animalId.length < 36) {
        setSponsorships([]);
        setTotalRaised(0);
        return;
      }
      
   const { data, error } = await supabase
  .from('sponsorships')
  .select('*')
  .eq('animal_id', animalId)
  .in('status', ['active', 'completed']);

      if (error) {
        console.error('Error fetching sponsorships:', error);
        setSponsorships([]);
        setTotalRaised(0);
        return;
      }

      setSponsorships(data || []);
      
      // Calculate total raised
      const total = (data || []).reduce((sum, sponsorship) => sum + Number(sponsorship.amount), 0);
      setTotalRaised(total);
    } catch (error) {
      console.error('Error fetching sponsorships:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (animalId) {
      fetchSponsorships();
    }
  }, [animalId]);

  return {
    sponsorships,
    loading,
    totalRaised,
    refetch: fetchSponsorships
  };
}