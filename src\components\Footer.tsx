import { Heart, Facebook, Instagram } from "lucide-react";

export const Footer = () => {
  return (
    <footer className="bg-card border-t border-border py-8 px-4">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* Logo and Copyright */}
          <div className="flex items-center gap-4">
            <img 
              src="/logo.PNG" 
              alt="Rescue Stories Logo"
              className="h-8 w-auto"
              onError={(e) => {
                // Fallback to heart icon if logo fails to load
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <div className="gradient-primary p-1.5 rounded-lg hidden">
              <Heart className="h-4 w-4 text-primary-foreground" />
            </div>
            <p className="text-muted-foreground">&copy; 2024 Rescue Stories CRM. Saving lives, one animal at a time.</p>
          </div>
          
          {/* Social Media Icons */}
          <div className="flex items-center gap-4">
            <span className="text-sm text-muted-foreground">Follow us:</span>
            <a 
              href="https://www.facebook.com/david.loop.3" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label="Follow us on Facebook"
            >
              <Facebook className="h-5 w-5" />
            </a>
            <a 
              href="https://www.instagram.com/davidloop65/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label="Follow us on Instagram"
            >
              <Instagram className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};
