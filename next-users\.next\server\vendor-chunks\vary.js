"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vary";
exports.ids = ["vendor-chunks/vary"];
exports.modules = {

/***/ "(rsc)/../node_modules/vary/index.js":
/*!*************************************!*\
  !*** ../node_modules/vary/index.js ***!
  \*************************************/
/***/ ((module) => {

eval("/*!\n * vary\n * Copyright(c) 2014-2017 <PERSON>\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n */\n\nmodule.exports = vary\nmodule.exports.append = append\n\n/**\n * RegExp to match field-name in RFC 7230 sec 3.2\n *\n * field-name    = token\n * token         = 1*tchar\n * tchar         = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" / \"*\"\n *               / \"+\" / \"-\" / \".\" / \"^\" / \"_\" / \"`\" / \"|\" / \"~\"\n *               / DIGIT / ALPHA\n *               ; any VCHAR, except delimiters\n */\n\nvar FIELD_NAME_REGEXP = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/\n\n/**\n * Append a field to a vary header.\n *\n * @param {String} header\n * @param {String|Array} field\n * @return {String}\n * @public\n */\n\nfunction append (header, field) {\n  if (typeof header !== 'string') {\n    throw new TypeError('header argument is required')\n  }\n\n  if (!field) {\n    throw new TypeError('field argument is required')\n  }\n\n  // get fields array\n  var fields = !Array.isArray(field)\n    ? parse(String(field))\n    : field\n\n  // assert on invalid field names\n  for (var j = 0; j < fields.length; j++) {\n    if (!FIELD_NAME_REGEXP.test(fields[j])) {\n      throw new TypeError('field argument contains an invalid header name')\n    }\n  }\n\n  // existing, unspecified vary\n  if (header === '*') {\n    return header\n  }\n\n  // enumerate current values\n  var val = header\n  var vals = parse(header.toLowerCase())\n\n  // unspecified vary\n  if (fields.indexOf('*') !== -1 || vals.indexOf('*') !== -1) {\n    return '*'\n  }\n\n  for (var i = 0; i < fields.length; i++) {\n    var fld = fields[i].toLowerCase()\n\n    // append value (case-preserving)\n    if (vals.indexOf(fld) === -1) {\n      vals.push(fld)\n      val = val\n        ? val + ', ' + fields[i]\n        : fields[i]\n    }\n  }\n\n  return val\n}\n\n/**\n * Parse a vary header into an array.\n *\n * @param {String} header\n * @return {Array}\n * @private\n */\n\nfunction parse (header) {\n  var end = 0\n  var list = []\n  var start = 0\n\n  // gather tokens\n  for (var i = 0, len = header.length; i < len; i++) {\n    switch (header.charCodeAt(i)) {\n      case 0x20: /*   */\n        if (start === end) {\n          start = end = i + 1\n        }\n        break\n      case 0x2c: /* , */\n        list.push(header.substring(start, end))\n        start = end = i + 1\n        break\n      default:\n        end = i + 1\n        break\n    }\n  }\n\n  // final token\n  list.push(header.substring(start, end))\n\n  return list\n}\n\n/**\n * Mark that a request is varied on a header field.\n *\n * @param {Object} res\n * @param {String|Array} field\n * @public\n */\n\nfunction vary (res, field) {\n  if (!res || !res.getHeader || !res.setHeader) {\n    // quack quack\n    throw new TypeError('res argument is required')\n  }\n\n  // get existing header\n  var val = res.getHeader('Vary') || ''\n  var header = Array.isArray(val)\n    ? val.join(', ')\n    : String(val)\n\n  // set new header\n  if ((val = append(header, field))) {\n    res.setHeader('Vary', val)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/vary/index.js\n");

/***/ })

};
;