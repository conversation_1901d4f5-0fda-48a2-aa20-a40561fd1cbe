import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PaymentDialog } from "@/components/PaymentDialog";
import { Heart, Utensils, Shield, Stethoscope, Gamepad2, Star } from "lucide-react";

export function SponsorshipOptions({ animalName = "Luna", animalId = "1", onSponsorshipSuccess }: { 
  animalName?: string; 
  animalId?: string;
  onSponsorshipSuccess?: () => void;
}) {
  const [selectedSponsor, setSelectedSponsor] = useState<{
    type: string;
    amount: number;
  } | null>(null);
  const sponsorshipOptions = [
    {
      icon: Heart,
      title: "Full Sponsorship",
      description: `Become ${animalName}'s guardian angel`,
      amount: "$50/month",
      numericAmount: 50,
      popular: true,
      color: "text-primary"
    },
    {
      icon: Utensils,
      title: "Sponsor a Meal",
      description: "Nutritious food for a week",
      amount: "$25",
      numericAmount: 25,
      popular: false,
      color: "text-warning"
    },
    {
      icon: Shield,
      title: "Sponsor Vaccine",
      description: "Essential vaccinations",
      amount: "$40",
      numericAmount: 40,
      popular: false,
      color: "text-success"
    },
    {
      icon: Stethoscope,
      title: "Sponsor Surgery",
      description: "Life-saving medical care",
      amount: "$150",
      numericAmount: 150,
      popular: false,
      color: "text-destructive"
    },
    {
      icon: Gamepad2,
      title: "Sponsor a Toy",
      description: "Enrichment and joy",
      amount: "$15",
      numericAmount: 15,
      popular: false,
      color: "text-accent-foreground"
    }
  ];

  return (
    <div className="space-y-6" data-sponsorship-options>
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-foreground mb-2">Support {animalName}'s Journey</h3>
        <p className="text-muted-foreground">Choose how you'd like to help {animalName} thrive</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
        {sponsorshipOptions.map((option, index) => {
          const IconComponent = option.icon;
          return (
            <Card 
              key={index} 
              className={`relative overflow-hidden hover:shadow-medium transition-smooth cursor-pointer border ${
                option.popular ? 'ring-2 ring-primary border-primary' : 'hover:border-primary/50'
              }`}
            >
              {option.popular && (
                <Badge className="absolute top-3 right-3 gradient-primary text-primary-foreground">
                  <Star className="h-3 w-3 mr-1" />
                  Popular
                </Badge>
              )}
              
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg bg-muted ${option.color}`}>
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{option.title}</CardTitle>
                    <p className="text-2xl font-bold text-primary">{option.amount}</p>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">{option.description}</p>
                <Button 
                  className={`w-full ${option.popular ? 'gradient-primary text-primary-foreground' : ''}`}
                  variant={option.popular ? "default" : "outline"}
                  onClick={() => setSelectedSponsor({
                    type: option.title,
                    amount: option.numericAmount
                  })}
                >
                  Sponsor Now
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <PaymentDialog
        isOpen={!!selectedSponsor}
        onClose={() => setSelectedSponsor(null)}
        animalId={animalId}
        animalName={animalName}
        sponsorType={selectedSponsor?.type || ""}
        amount={selectedSponsor?.amount || 0}
        onSuccess={() => {
          onSponsorshipSuccess?.();
          setSelectedSponsor(null);
        }}
      />
    </div>
  );
}