
-- First, let's check what policies currently exist and then fix them
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.users;

-- Create a more explicit INSERT policy that should definitely work
CREATE POLICY "Allow user profile creation during signup" 
ON public.users 
FOR INSERT 
WITH CHECK (auth.uid() = id);

-- Also ensure we have proper SELECT policy for user's own profile
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
CREATE POLICY "Users can view their own profile" 
ON public.users 
FOR SELECT 
USING (auth.uid() = id OR is_staff_or_admin());

-- And proper UPDATE policy
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
CREATE POLICY "Users can update their own profile" 
ON public.users 
FOR UPDATE 
USING (auth.uid() = id);
