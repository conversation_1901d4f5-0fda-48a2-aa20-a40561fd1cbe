import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Heart, Utensils, Shield, Stethoscope, Gamepad2, Users, TrendingUp } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface SponsorshipData {
  sponsor_type: string;
  total_amount: number;
  sponsor_count: number;
  target_amount: number;
}

interface SponsorshipProgressProps {
  animalId: string;
  animalName: string;
  totalRaised: number;
  onSponsorClick?: () => void;
}

export function SponsorshipProgress({ 
  animalId, 
  animalName, 
  totalRaised,
  onSponsorClick 
}: SponsorshipProgressProps) {
  const [sponsorshipData, setSponsorshipData] = useState<SponsorshipData[]>([]);
  const [loading, setLoading] = useState(true);

  const sponsorshipGoal = 500; // Default goal
  const sponsorshipPercentage = Math.min((totalRaised / sponsorshipGoal) * 100, 100);

  const sponsorTypeConfig = {
    'full sponsorship': {
      icon: Heart,
      color: 'text-primary',
      bgColor: 'bg-primary/10',
      target: 50
    },
    'sponsor a meal': {
      icon: Utensils,
      color: 'text-warning',
      bgColor: 'bg-warning/10',
      target: 25
    },
    'sponsor vaccine': {
      icon: Shield,
      color: 'text-success',
      bgColor: 'bg-success/10',
      target: 40
    },
    'sponsor surgery': {
      icon: Stethoscope,
      color: 'text-destructive',
      bgColor: 'bg-destructive/10',
      target: 150
    },
    'sponsor a toy': {
      icon: Gamepad2,
      color: 'text-accent-foreground',
      bgColor: 'bg-accent/10',
      target: 15
    }
  };

  useEffect(() => {
    fetchSponsorshipBreakdown();
  }, [animalId, totalRaised]);

  const fetchSponsorshipBreakdown = async () => {
    try {
      setLoading(true);
      
      if (!animalId || animalId.length < 36) {
        setSponsorshipData([]);
        return;
      }

      const { data, error } = await supabase
        .from('sponsorships')
        .select('sponsor_type, amount')
        .eq('animal_id', animalId)
       .in('status', ['active', 'completed'])

      if (error) {
        console.error('Error fetching sponsorship breakdown:', error);
        return;
      }

      // Group by sponsor type
      const breakdown = (data || []).reduce((acc, sponsorship) => {
        const type = sponsorship.sponsor_type;
        if (!acc[type]) {
          acc[type] = {
            sponsor_type: type,
            total_amount: 0,
            sponsor_count: 0,
            target_amount: sponsorTypeConfig[type as keyof typeof sponsorTypeConfig]?.target || 50
          };
        }
        acc[type].total_amount += Number(sponsorship.amount);
        acc[type].sponsor_count += 1;
        return acc;
      }, {} as Record<string, SponsorshipData>);

      setSponsorshipData(Object.values(breakdown));
    } catch (error) {
      console.error('Error fetching sponsorship breakdown:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatSponsorType = (type: string) => {
    return type.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className="space-y-6">
      {/* Overall Progress */}
      <Card className="shadow-soft">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-primary" />
            Sponsorship Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span className="text-muted-foreground">Progress</span>
              <span className="font-medium">${totalRaised} / ${sponsorshipGoal}</span>
            </div>
            <Progress value={sponsorshipPercentage} className="h-3" />
            <p className="text-xs text-muted-foreground mt-1">
              {Math.round(sponsorshipPercentage)}% of monthly goal reached
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-primary">${totalRaised}</p>
              <p className="text-xs text-muted-foreground">Total Raised</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-success">
                {sponsorshipData.reduce((sum, data) => sum + data.sponsor_count, 0)}
              </p>
              <p className="text-xs text-muted-foreground">Sponsors</p>
            </div>
          </div>

          {onSponsorClick && (
            <Button 
              onClick={onSponsorClick}
              className="w-full gradient-primary text-primary-foreground"
            >
              <Heart className="mr-2 h-4 w-4" />
              Sponsor {animalName}
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Sponsorship Breakdown */}
      {sponsorshipData.length > 0 && (
        <Card className="shadow-soft">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Sponsorship Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {sponsorshipData.map((data) => {
              const config = sponsorTypeConfig[data.sponsor_type as keyof typeof sponsorTypeConfig];
              const IconComponent = config?.icon || Heart;
              const progress = Math.min((data.total_amount / data.target_amount) * 100, 100);

              return (
                <div key={data.sponsor_type} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`p-1.5 rounded-lg ${config?.bgColor || 'bg-muted'}`}>
                        <IconComponent className={`h-4 w-4 ${config?.color || 'text-muted-foreground'}`} />
                      </div>
                      <span className="font-medium text-sm">
                        {formatSponsorType(data.sponsor_type)}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {data.sponsor_count} sponsor{data.sponsor_count !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                    <span className="text-sm font-medium">
                      ${data.total_amount} / ${data.target_amount}
                    </span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              );
            })}
          </CardContent>
        </Card>
      )}

      {/* Call to Action */}
      <Card className="shadow-soft border-primary/20">
        <CardContent className="p-4 text-center">
          <Users className="h-8 w-8 text-primary mx-auto mb-2" />
          <h4 className="font-semibold text-foreground mb-1">
            Help {animalName} Reach Their Goal
          </h4>
          <p className="text-sm text-muted-foreground mb-3">
            Every contribution makes a difference in {animalName}'s journey to finding their forever home.
          </p>
          {onSponsorClick && (
            <Button 
              onClick={onSponsorClick}
              variant="outline" 
              className="w-full"
            >
              Choose Sponsorship Type
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

