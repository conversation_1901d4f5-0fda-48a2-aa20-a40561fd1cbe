import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Heart, Menu, Users, Calendar, Trophy, LogOut, Settings, User, Search } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useRandomAnimal } from "@/hooks/useRandomAnimal";

export const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, userProfile, signOut, isAdmin, isStaff } = useAuth();
  const { randomAnimal } = useRandomAnimal();
  const navigate = useNavigate();

  const handleDonateClick = () => {
    if (randomAnimal) {
      navigate(`/animal/${randomAnimal.id}`);
    } else {
      // Fallback to animals page if no random animal available
      navigate('/animals');
    }
  };

  return (
    <header className="sticky top-0 z-50 bg-card/80 backdrop-blur-sm border-b border-border shadow-soft">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2">
            <img
              src="/logo.PNG"
              alt="Rescue Stories Logo"
              className="h-10 w-auto"
              onError={(e) => {
                // Fallback to heart icon if logo fails to load
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <div className="gradient-primary p-2 rounded-xl hidden">
              <Heart className="h-6 w-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="font-bold text-xl text-foreground">Rescue Stories</h1>
              <p className="text-xs text-muted-foreground">Saving Lives Together</p>
            </div>
          </Link>

          {/* Navigation - Hidden on mobile */}
          <nav className="hidden md:flex items-center gap-6">
            <Link to="/animals" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors">
              <Users className="h-5 w-5" />
              <span>Animals</span>
            </Link>
            <Link to="/staff-login" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors">
              <User className="h-5 w-5" />
              <span>Staff Login</span>
            </Link>
            {isStaff && (
              <Link to="/staff" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors">
                <Calendar className="h-5 w-5" />
                <span>Staff Portal</span>
              </Link>
            )}
            {isAdmin && (
              <Link to="/admin" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors">
                <Settings className="h-5 w-5" />
                <span>Admin Dashboard</span>
              </Link>
            )}
            <Link to="/success-stories" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors">
              <Trophy className="h-5 w-5" />
              <span>Success Stories</span>
            </Link>
          </nav>

          {/* Search and Actions */}
          <div className="flex items-center gap-3">
            <div className="relative hidden sm:block">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Search animals..." 
                className="pl-10 w-64"
              />
            </div>
            
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    {userProfile?.full_name || user.email}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Profile</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={signOut}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex gap-2">
                <Link to="/auth">
                  <Button variant="outline" size="sm">
                    Sign In
                  </Button>
                </Link>
                <Button size="sm" className="gradient-primary" onClick={handleDonateClick}>
                  Donate Now
                </Button>
              </div>
            )}

            {/* Mobile menu */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" className="md:hidden">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                <div className="flex flex-col gap-4 mt-8">
                  <Link to="/animals" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors py-2">
                    <Users className="h-5 w-5" />
                    <span>Animals</span>
                  </Link>
                  <Link to="/staff-login" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors py-2">
                    <User className="h-5 w-5" />
                    <span>Staff Login</span>
                  </Link>
                  {isStaff && (
                    <Link to="/staff" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors py-2">
                      <Calendar className="h-5 w-5" />
                      <span>Staff Portal</span>
                    </Link>
                  )}
                  {isAdmin && (
                    <Link to="/admin" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors py-2">
                      <Settings className="h-5 w-5" />
                      <span>Admin Dashboard</span>
                    </Link>
                  )}
                  <Link to="/success-stories" className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors py-2">
                    <Trophy className="h-5 w-5" />
                    <span>Success Stories</span>
                  </Link>
                  
                  {user ? (
                    <div className="space-y-2 pt-4 border-t">
                      <p className="text-sm font-medium">{userProfile?.full_name || user.email}</p>
                      <Button variant="outline" size="sm" className="w-full" onClick={signOut}>
                        <LogOut className="h-4 w-4 mr-2" />
                        Sign Out
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2 pt-4 border-t">
                      <Link to="/auth">
                        <Button variant="outline" size="sm" className="w-full">
                          Sign In
                        </Button>
                      </Link>
                      <Button size="sm" className="gradient-primary w-full" onClick={handleDonateClick}>
                        Donate Now
                      </Button>
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};