// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://opyvpzqzybqmetnbjwli.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9weXZwenF6eWJxbWV0bmJqd2xpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYwNDc2ODAsImV4cCI6MjA3MTYyMzY4MH0.KgSkPSLFzQvJJfE_7QX8byDmvfj3V98qn3dryxIDmv0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});