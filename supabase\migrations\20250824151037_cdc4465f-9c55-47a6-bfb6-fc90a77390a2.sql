-- Create users table for additional user information
CREATE TABLE public.users (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'staff', 'volunteer', 'foster', 'user')),
  full_name TEXT,
  phone TEXT,
  address TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create animals table
CREATE TABLE public.animals (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  species TEXT NOT NULL CHECK (species IN ('dog', 'cat', 'other')),
  breed TEXT,
  age TEXT,
  gender TEXT NOT NULL DEFAULT 'unknown' CHECK (gender IN ('male', 'female', 'unknown')),
  size TEXT NOT NULL DEFAULT 'medium' CHECK (size IN ('small', 'medium', 'large')),
  status TEXT NOT NULL DEFAULT 'available' CHECK (status IN ('available', 'pending', 'adopted', 'fostered', 'medical_hold')),
  intake_date DATE NOT NULL DEFAULT CURRENT_DATE,
  rescue_location TEXT,
  intake_story TEXT,
  medical_notes TEXT,
  behavioral_notes TEXT,
  photos TEXT[] DEFAULT '{}',
  videos TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create animal_updates table
CREATE TABLE public.animal_updates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  animal_id UUID NOT NULL REFERENCES public.animals(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  update_type TEXT NOT NULL CHECK (update_type IN ('medical', 'behavioral', 'general', 'milestone')),
  content TEXT NOT NULL,
  author_name TEXT NOT NULL,
  author_role TEXT NOT NULL,
  media_urls TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create sponsorships table
CREATE TABLE public.sponsorships (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  animal_id UUID NOT NULL REFERENCES public.animals(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  sponsor_type TEXT NOT NULL CHECK (sponsor_type IN ('animal', 'meal', 'vaccine', 'surgery', 'toy')),
  amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  target_amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.animals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.animal_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sponsorships ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view their own profile" ON public.users
FOR SELECT USING (auth.uid()::text = id OR EXISTS (
  SELECT 1 FROM public.users u WHERE u.id = auth.uid()::text AND u.role IN ('admin', 'staff')
));

CREATE POLICY "Users can update their own profile" ON public.users
FOR UPDATE USING (auth.uid()::text = id);

CREATE POLICY "Admins can manage all users" ON public.users
FOR ALL USING (EXISTS (
  SELECT 1 FROM public.users u WHERE u.id = auth.uid()::text AND u.role = 'admin'
));

-- RLS Policies for animals table (public read, staff+ can modify)
CREATE POLICY "Anyone can view animals" ON public.animals
FOR SELECT USING (true);

CREATE POLICY "Staff can manage animals" ON public.animals
FOR ALL USING (EXISTS (
  SELECT 1 FROM public.users u WHERE u.id = auth.uid()::text AND u.role IN ('admin', 'staff')
));

-- RLS Policies for animal_updates table
CREATE POLICY "Anyone can view animal updates" ON public.animal_updates
FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create updates" ON public.animal_updates
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND user_id = auth.uid()::text);

CREATE POLICY "Staff can manage all updates" ON public.animal_updates
FOR ALL USING (EXISTS (
  SELECT 1 FROM public.users u WHERE u.id = auth.uid()::text AND u.role IN ('admin', 'staff')
));

-- RLS Policies for sponsorships table
CREATE POLICY "Anyone can view sponsorships" ON public.sponsorships
FOR SELECT USING (true);

CREATE POLICY "Users can create their own sponsorships" ON public.sponsorships
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND user_id = auth.uid()::text);

CREATE POLICY "Users can update their own sponsorships" ON public.sponsorships
FOR UPDATE USING (user_id = auth.uid()::text);

CREATE POLICY "Staff can manage all sponsorships" ON public.sponsorships
FOR ALL USING (EXISTS (
  SELECT 1 FROM public.users u WHERE u.id = auth.uid()::text AND u.role IN ('admin', 'staff')
));

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON public.users
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_animals_updated_at
  BEFORE UPDATE ON public.animals
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();