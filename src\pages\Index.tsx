import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { HeroSection } from "@/components/HeroSection";
import { AnimalCard } from "@/components/AnimalCard";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Heart, Stethoscope, Home, Users, ArrowRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Animal } from "@/types/database";
import { toast } from "@/hooks/use-toast";
import { useRandomAnimal } from "@/hooks/useRandomAnimal";

const Index = () => {
  const [featuredAnimals, setFeaturedAnimals] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { randomAnimal } = useRandomAnimal();
  const navigate = useNavigate();

  const handleSponsorClick = () => {
    if (randomAnimal) {
      navigate(`/animal/${randomAnimal.id}`);
    } else {
      // Fallback to animals page if no random animal available
      navigate('/animals');
    }
  };

  useEffect(() => {
    fetchFeaturedAnimals();
  }, []);

  const fetchFeaturedAnimals = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('animals')
        .select('*')
        .eq('status', 'available')
        .order('created_at', { ascending: false })
        .limit(3);

      if (error) throw error;

      // Map database animals to the format expected by AnimalCard
      const mappedAnimals = ((data || []) as Animal[]).map(animal => ({
        id: animal.id,
        name: animal.name,
        type: animal.species === 'dog' ? 'Dog' : animal.species === 'cat' ? 'Cat' : 'Other',
        breed: animal.breed || 'Mixed breed',
        age: animal.age || 'Unknown age',
        status: 'Available' as const,
        image: animal.photos?.[0] || '/placeholder.svg',
        location: animal.rescue_location || 'Austin, TX',
        intakeDate: new Date(animal.intake_date).toLocaleDateString(),
        sponsorshipGoal: 500,
        sponsorshipCurrent: 0,
        story: animal.intake_story || 'This wonderful animal is looking for their forever home.'
      }));

      setFeaturedAnimals(mappedAnimals);
    } catch (error: any) {
      console.error('Error fetching featured animals:', error);
      toast({
        title: "Error loading featured animals",
        description: error.message,
        variant: "destructive",
      });
      setFeaturedAnimals([]);
    } finally {
      setLoading(false);
    }
  };

  const impactStats = [
    { icon: Heart, number: "247", label: "Lives Saved", color: "text-primary" },
    { icon: Stethoscope, number: "89", label: "Medical Procedures", color: "text-success" },
    { icon: Home, number: "156", label: "Forever Homes", color: "text-warning" },
    { icon: Users, number: "234", label: "Active Volunteers", color: "text-accent-foreground" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main>
        <HeroSection />
        
        {/* Featured Animals Section */}
        <section className="py-16 px-4">
          <div className="container mx-auto">
            <div className="text-center mb-12">
              <Badge className="mb-4 bg-primary/10 text-primary">
                <Heart className="h-3 w-3 mr-1" />
                Featured Rescues
              </Badge>
              <h2 className="text-4xl font-bold text-foreground mb-4">
                Animals Looking for Love
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Meet our wonderful animals who are ready to become part of your family. 
                Each one has a unique story and is waiting for their forever home.
              </p>
            </div>

            {loading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                {featuredAnimals.length > 0 ? (
                  featuredAnimals.map((animal) => (
                    <AnimalCard key={animal.id} {...animal} />
                  ))
                ) : (
                  <div className="col-span-full text-center py-12">
                    <p className="text-muted-foreground">No animals available at the moment. Check back soon!</p>
                  </div>
                )}
              </div>
            )}

            <div className="text-center space-y-4">
              <Button size="lg" variant="outline" className="w-full" asChild>
                <Link to="/animals" className="flex items-center justify-center">
                  View All Animals
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Impact Stats Section */}
        <section className="py-16 px-4 bg-muted/50">
          <div className="container mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-foreground mb-4">
                Our Impact Together
              </h2>
              <p className="text-xl text-muted-foreground">
                Thanks to supporters like you, we're making a real difference in animal lives.
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {impactStats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <Card key={index} className="text-center p-6 shadow-soft hover:shadow-medium transition-smooth">
                    <CardContent className="pt-6">
                      <div className={`inline-flex p-3 rounded-full bg-muted mb-4 ${stat.color}`}>
                        <IconComponent className="h-8 w-8" />
                      </div>
                      <div className="text-3xl font-bold text-foreground mb-1">{stat.number}</div>
                      <div className="text-muted-foreground font-medium">{stat.label}</div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className="py-16 px-4 gradient-hero">
          <div className="container mx-auto text-center">
            <h2 className="text-4xl font-bold text-white mb-4">
              Ready to Make a Difference?
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Join our community of animal lovers. Whether you adopt, foster, volunteer, or sponsor, 
              every action helps save lives.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-primary hover:bg-white/90 font-semibold px-8" onClick={handleSponsorClick}>
                <Heart className="mr-2 h-5 w-5" />
                Start Sponsoring
              </Button>
              <Button size="lg" variant="outline" className="border-white hover:bg-white/10 font-semibold px-8" asChild>
                <Link to="/animals">
                  Learn About Volunteering
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Index;