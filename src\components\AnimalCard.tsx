import { Link } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Heart, Share2, MapPin, Calendar } from "lucide-react";

interface AnimalCardProps {
  id: string;
  name: string;
  type: string;
  breed: string;
  age: string;
  status: "Available" | "Fostered" | "Adopted" | "Medical Care";
  image: string;
  location: string;
  intakeDate: string;
  sponsorshipGoal: number;
  sponsorshipCurrent: number;
  story: string;
}

export function AnimalCard({ 
  id, 
  name, 
  type, 
  breed, 
  age, 
  status, 
  image, 
  location, 
  intakeDate, 
  sponsorshipGoal, 
  sponsorshipCurrent, 
  story 
}: AnimalCardProps) {
  const sponsorshipPercentage = (sponsorshipCurrent / sponsorshipGoal) * 100;
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Available": return "bg-success text-success-foreground";
      case "Fostered": return "bg-warning text-warning-foreground";
      case "Adopted": return "bg-primary text-primary-foreground";
      case "Medical Care": return "bg-destructive text-destructive-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  return (
    <Card className="overflow-hidden shadow-medium hover:shadow-strong transition-smooth group cursor-pointer">
      <div className="relative">
        <img 
          src={image} 
          alt={`${name} - ${type}`}
          className="w-full h-48 object-cover group-hover:scale-105 transition-smooth"
        />
        <Badge className={`absolute top-3 left-3 ${getStatusColor(status)}`}>
          {status}
        </Badge>
        <Button
          size="sm"
          variant="secondary"
          className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-smooth"
        >
          <Share2 className="h-4 w-4" />
        </Button>
      </div>
      
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold text-foreground">{name}</CardTitle>
          <Heart className="h-5 w-5 text-muted-foreground hover:text-primary cursor-pointer transition-smooth" />
        </div>
        <div className="text-sm text-muted-foreground">
          {breed} • {age} • {type}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <MapPin className="h-4 w-4" />
          {location}
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          Rescued: {intakeDate}
        </div>

        <p className="text-sm text-foreground line-clamp-2">{story}</p>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Sponsorship Progress</span>
            <span className="font-medium">${sponsorshipCurrent} / ${sponsorshipGoal}</span>
          </div>
          <Progress value={sponsorshipPercentage} className="h-2" />
        </div>

        <div className="flex gap-2">
          <Button variant="default" size="sm" className="flex-1" asChild>
            <Link to={`/animal/${id}`}>
              Sponsor {name}
            </Link>
          </Button>
          <Button variant="outline" size="sm" className="flex-1" asChild>
            <Link to={`/animal/${id}`}>
              Learn More
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}