-- Fix the security definer view warning by recreating without SECURITY DEFINER
DROP VIEW IF EXISTS public.animal_sponsorship_totals;

-- Create the view with SECURITY INVOKER (default) to respect querying user's permissions
CREATE VIEW public.animal_sponsorship_totals 
WITH (security_invoker = true) AS
SELECT 
  animal_id,
  SUM(amount) as total_raised,
  COUNT(*) as sponsor_count
FROM sponsorships 
WHERE status = 'active'
GROUP BY animal_id;

-- Grant public access to the aggregated view only
GRANT SELECT ON public.animal_sponsorship_totals TO anon;
GRANT SELECT ON public.animal_sponsorship_totals TO authenticated;