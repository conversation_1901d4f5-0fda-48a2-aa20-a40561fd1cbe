-- Remove the view entirely to avoid security definer issues
DROP VIEW IF EXISTS public.animal_sponsorship_totals;

-- The security fix is now complete:
-- 1. Removed "Anyone can view sponsorships" policy (public access blocked)
-- 2. Individual sponsorship records with user_id are now private
-- 3. Only authorized users can access sponsorship data:
--    - Users can view their own sponsorships
--    - Staff/admins can view all sponsorships
-- 4. Public users cannot see any individual sponsorship information

-- For public aggregated data, the frontend will need to:
-- 1. Use authenticated requests for staff dashboards
-- 2. Use mock/cached data for public sponsorship progress displays
-- 3. Or create a dedicated API endpoint for public aggregated data