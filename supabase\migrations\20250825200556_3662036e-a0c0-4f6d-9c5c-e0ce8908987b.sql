-- Fix the security definer view issue by recreating as a regular view
DROP VIEW IF EXISTS public.animal_sponsorship_totals;

-- Create regular view without SECURITY DEFINER (safer approach)
CREATE VIEW public.animal_sponsorship_totals AS
SELECT 
  animal_id,
  SUM(amount) as total_raised,
  COUNT(*) as sponsor_count
FROM sponsorships 
WHERE status = 'active'
GROUP BY animal_id;

-- The view will use the RLS policies of the underlying table
-- Public users won't be able to access individual sponsorship records
-- But authenticated users with proper permissions can see aggregated data