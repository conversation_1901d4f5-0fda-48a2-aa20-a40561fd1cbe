import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Helper to set CORS headers
function setCorsHeaders(response) {
  response.headers.set('Access-Control-Allow-Origin', '*'); // Or 'http://localhost:8080'
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
  return response;
}

// Handle preflight (OPTIONS) requests
export async function OPTIONS() {
  const response = NextResponse.json({}, { status: 200 });
  return setCorsHeaders(response);
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { email, full_name, phone, address, role } = body;

    console.log('Received invite request:', { email, full_name, phone, address, role });

    // Create the user in Supabase Auth
    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password: 'password',
      email_confirm: false,
      invite:true,
      user_metadata: { full_name, phone, address, role }
    });
    

    console.log('Supabase createUser response:', { data, error });

    if (error) {
      const response = NextResponse.json({ error: error.message }, { status: 400 });
      return setCorsHeaders(response);
    }

    // Optionally, insert into your users table
    const { error: dbError } = await supabase.from('users').insert([{
      id: data.user.id,
      full_name,
      email,
      phone,
      address,
      role,
    }]);

    if (dbError) {
      console.log('Error inserting into users table:', dbError);
    } else {
      console.log('Inserted user into users table:', data.user.id);
    }

    const response = NextResponse.json({ user: data.user });
    return setCorsHeaders(response);
  } catch (err) {
    console.log('API error:', err);
    const response = NextResponse.json({ error: err.message || 'Unknown error' }, { status: 500 });
    return setCorsHeaders(response);
  }
}