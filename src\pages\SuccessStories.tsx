import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Heart, Trophy, Users, Calendar, MapPin, Star, Home } from "lucide-react";
import { useRandomAnimal } from "@/hooks/useRandomAnimal";
import buddyImage from "@/assets/buddy-dog.jpg";

const SuccessStories = () => {
  const { randomAnimal } = useRandomAnimal();
  const navigate = useNavigate();

  const handleSponsorClick = () => {
    if (randomAnimal) {
      navigate(`/animal/${randomAnimal.id}`);
    } else {
      // Fallback to animals page if no random animal available
      navigate('/animals');
    }
  };

  const successStories = [
    {
      id: "1",
      animalName: "Max",
      type: "Dog",
      breed: "Labrador Retriever",
      adoptionDate: "March 2024",
      rescueDate: "December 2023", 
      familyName: "The Johnson Family",
      location: "Austin, TX",
      image: buddyImage,
      story: "<PERSON> came to us scared and malnourished after being found wandering the streets. With lots of love, medical care, and patience, he transformed into the most loving companion. The <PERSON> family fell in love with him immediately during their first visit. Now Max enjoys daily walks, playing with his new dog siblings, and lots of cuddles on the couch.",
      beforeImage: buddyImage,
      afterImage: buddyImage,
      sponsorshipRaised: 850,
      timeInCare: "4 months",
      specialNotes: "Max overcame severe anxiety and is now a certified therapy dog helping other rescue animals."
    },
    {
      id: "2",
      animalName: "Bella", 
      type: "Cat",
      breed: "Domestic Shorthair",
      adoptionDate: "February 2024",
      rescueDate: "October 2023",
      familyName: "Sarah & Mike Wilson",
      location: "Austin, TX", 
      image: buddyImage,
      story: "Bella was rescued from a hoarding situation with 20+ other cats. She was extremely shy and fearful of humans. Through our foster program and behavioral therapy, Bella learned to trust again. The Wilsons provided the perfect quiet home where Bella could continue healing. She now loves window perches and chin scratches.",
      beforeImage: buddyImage,
      afterImage: buddyImage,
      sponsorshipRaised: 420,
      timeInCare: "5 months",
      specialNotes: "Bella helps her new family foster other shy cats, showing them it's safe to trust humans."
    },
    {
      id: "3",
      animalName: "Charlie",
      type: "Dog", 
      breed: "Border Collie Mix",
      adoptionDate: "January 2024",
      rescueDate: "August 2023",
      familyName: "The Rodriguez Family",
      location: "Austin, TX",
      image: buddyImage, 
      story: "Charlie was surrendered due to his high energy and need for mental stimulation. His previous family couldn't provide the exercise and training he needed. The Rodriguez family, experienced with active breeds, knew Charlie was perfect for their lifestyle. He now competes in agility competitions and helps teach their children responsibility.",
      beforeImage: buddyImage,
      afterImage: buddyImage,
      sponsorshipRaised: 650,
      timeInCare: "6 months", 
      specialNotes: "Charlie earned his Canine Good Citizen certification and volunteers at local schools for reading programs."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-success/10 text-success border-success/20">
            <Heart className="h-3 w-3 mr-1" />
            Happy Endings
          </Badge>
          <h1 className="text-5xl font-bold text-foreground mb-4">Success Stories</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Every adoption is a celebration! These heartwarming stories show the incredible 
            transformations that happen when rescued animals find their forever families.
          </p>
        </div>

        {/* Impact Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="text-center p-6 shadow-soft hover:shadow-medium transition-smooth">
            <div className="inline-flex p-3 rounded-full bg-success/10 text-success mb-4">
              <Home className="h-8 w-8" />
            </div>
            <div className="text-3xl font-bold text-foreground mb-1">156</div>
            <div className="text-muted-foreground font-medium">Forever Homes Found</div>
          </Card>

          <Card className="text-center p-6 shadow-soft hover:shadow-medium transition-smooth">
            <div className="inline-flex p-3 rounded-full bg-primary/10 text-primary mb-4">
              <Heart className="h-8 w-8" />
            </div>
            <div className="text-3xl font-bold text-foreground mb-1">$45,600</div>
            <div className="text-muted-foreground font-medium">Raised Through Sponsorships</div>
          </Card>

          <Card className="text-center p-6 shadow-soft hover:shadow-medium transition-smooth">
            <div className="inline-flex p-3 rounded-full bg-warning/10 text-warning mb-4">
              <Users className="h-8 w-8" />
            </div>
            <div className="text-3xl font-bold text-foreground mb-1">89</div>
            <div className="text-muted-foreground font-medium">Families Matched</div>
          </Card>

          <Card className="text-center p-6 shadow-soft hover:shadow-medium transition-smooth">
            <div className="inline-flex p-3 rounded-full bg-accent/10 text-accent-foreground mb-4">
              <Star className="h-8 w-8" />
            </div>
            <div className="text-3xl font-bold text-foreground mb-1">4.2</div>
            <div className="text-muted-foreground font-medium">Avg. Months in Care</div>
          </Card>
        </div>

        {/* Success Stories Grid */}
        <div className="space-y-12">
          {successStories.map((story, index) => (
            <Card key={story.id} className="overflow-hidden shadow-strong">
              <div className={`grid grid-cols-1 lg:grid-cols-2 ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                {/* Image Section */}
                <div className={`relative ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  <div className="aspect-video lg:aspect-auto lg:h-full">
                    <img 
                      src={story.image}
                      alt={`${story.animalName} in their new home`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <Badge className="absolute top-4 left-4 bg-success text-success-foreground">
                    <Home className="h-3 w-3 mr-1" />
                    Adopted!
                  </Badge>
                </div>

                {/* Content Section */}
                <div className="p-8 lg:p-12">
                  <div className="mb-6">
                    <h2 className="text-3xl font-bold text-foreground mb-2">{story.animalName}'s Journey</h2>
                    <p className="text-lg text-muted-foreground">
                      {story.breed} • Rescued {story.rescueDate} • Adopted {story.adoptionDate}
                    </p>
                  </div>

                  <p className="text-foreground leading-relaxed mb-8 text-lg">
                    {story.story}
                  </p>

                  {/* Key Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Users className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-semibold text-foreground">{story.familyName}</p>
                          <p className="text-sm text-muted-foreground">Forever Family</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <MapPin className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-semibold text-foreground">{story.location}</p>
                          <p className="text-sm text-muted-foreground">New Home</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Calendar className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-semibold text-foreground">{story.timeInCare}</p>
                          <p className="text-sm text-muted-foreground">Time in Our Care</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Heart className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-semibold text-foreground">${story.sponsorshipRaised}</p>
                          <p className="text-sm text-muted-foreground">Community Support</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Special Achievement */}
                  <Card className="bg-muted/50 border-primary/20">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Star className="h-5 w-5 text-primary mt-0.5" />
                        <div>
                          <h4 className="font-semibold text-foreground mb-1">Special Achievement</h4>
                          <p className="text-sm text-muted-foreground">{story.specialNotes}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <Card className="gradient-hero p-12 shadow-strong">
            <h3 className="text-4xl font-bold text-white mb-4">
              Ready to Create Your Own Success Story?
            </h3>
            <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
              Every animal deserves a happy ending. Browse our available animals and find 
              your perfect companion, or support our mission through sponsorship.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-primary hover:bg-white/90 font-semibold px-8" onClick={handleSponsorClick}>
                <Heart className="mr-2 h-5 w-5" />
                View Available Animals
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 font-semibold px-8" onClick={handleSponsorClick}>
                Start Sponsoring Today
              </Button>
            </div>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SuccessStories;