import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { CreditCard, Lock } from "lucide-react";

interface PaymentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  animalId: string;
  animalName: string;
  sponsorType: string;
  amount: number;
  onSuccess: () => void;
}

export function PaymentDialog({ 
  isOpen, 
  onClose, 
  animalId, 
  animalName, 
  sponsorType, 
  amount,
  onSuccess 
}: PaymentDialogProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [cardName, setCardName] = useState("");
  const { toast } = useToast();

  const formatCardNumber = (value: string) => {
    const cleanValue = value.replace(/\D/g, '');
    const formatted = cleanValue.replace(/(\d{4})(?=\d)/g, '$1 ');
    return formatted.substring(0, 19); // Max 16 digits + 3 spaces
  };

  const formatExpiryDate = (value: string) => {
    const cleanValue = value.replace(/\D/g, '');
    if (cleanValue.length >= 2) {
      return cleanValue.substring(0, 2) + '/' + cleanValue.substring(2, 4);
    }
    return cleanValue;
  };

  const handlePayment = async (e: React.FormEvent) => {
  e.preventDefault();

  if (!name || !email || !cardNumber || !expiryDate || !cvv || !cardName) {
    toast({
      title: "Please fill in all fields",
      variant: "destructive",
    });
    return;
  }

  setIsProcessing(true);

  try {
    // Test card logic
    const testCardRaw = cardNumber.replace(/\s/g, '');
    if (testCardRaw === "1234567734") {
      // Insert a fake/test sponsorship record
     const { error } = await supabase
  .from('sponsorships')
  .insert([{
    animal_id: animalId,
    sponsor_type: "animal",
    amount,
    target_amount: amount,
    status: 'completed',
    created_at: new Date().toISOString(),
    sponsor_name: name,
    sponsor_email: email,
  }]);

      if (error) throw error;

      toast({
        title: "Test Payment Successful!",
        description: `Simulated payment for ${animalName} using test card.`,
      });

      onSuccess();
      onClose();

      // Reset form
      setName("");
      setEmail("");
      setCardNumber("");
      setExpiryDate("");
      setCvv("");
      setCardName("");
      setIsProcessing(false);
      return;
    }

    // --- Real payment logic would go here (if you have it) ---

    // Get current user (if you want to require login for real payments)
    const { data: { user } } = await supabase.auth.getUser();

    // Create sponsorship record for real payment
    const { error } = await supabase
      .from('sponsorships')
      .insert({
        user_id: user?.id,
        animal_id: animalId,
        sponsor_type: sponsorType.toLowerCase(),
        amount,
        target_amount: amount,
        status: 'active',
        created_at: new Date().toISOString(),
        sponsor_name: name,
        sponsor_email: email,
      });

    if (error) throw error;

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    toast({
      title: "Payment Successful!",
      description: `Thank you for sponsoring ${animalName}! Your ${sponsorType.toLowerCase()} sponsorship of $${amount} has been processed.`,
    });

    onSuccess();
    onClose();

    // Reset form
    setName("");
    setEmail("");
    setCardNumber("");
    setExpiryDate("");
    setCvv("");
    setCardName("");

  } catch (error) {
    console.error('Payment error:', error);
    toast({
      title: "Payment Failed",
      description: "There was an error processing your payment. Please try again.",
      variant: "destructive",
    });
  } finally {
    setIsProcessing(false);
  }
};
  const getSponsorTypeDescription = (type: string) => {
    switch (type.toLowerCase()) {
      case 'full sponsorship':
        return 'Complete monthly care for this animal';
      case 'sponsor a meal':
        return 'Nutritious food for a week';
      case 'sponsor vaccine':
        return 'Essential vaccinations';
      case 'sponsor surgery':
        return 'Life-saving medical care';
      case 'sponsor a toy':
        return 'Enrichment and joy';
      default:
        return 'Support for this animal';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-primary" />
            Sponsor {animalName}
          </DialogTitle>
        </DialogHeader>

        <Card className="shadow-soft">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">{sponsorType}</CardTitle>
            <p className="text-sm text-muted-foreground">
              {getSponsorTypeDescription(sponsorType)}
            </p>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold text-primary">${amount}</span>
              <span className="text-sm text-muted-foreground">
                {sponsorType.includes('month') ? 'per month' : 'one-time'}
              </span>
            </div>
          </CardContent>
        </Card>

        <Separator />

        <form onSubmit={handlePayment} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              placeholder="John Doe"
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isProcessing}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isProcessing}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="cardName">Cardholder Name</Label>
            <Input
              id="cardName"
              placeholder="John Doe"
              value={cardName}
              onChange={(e) => setCardName(e.target.value)}
              disabled={isProcessing}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="cardNumber">Card Number</Label>
            <Input
              id="cardNumber"
              placeholder="1234 5678 9012 3456"
              value={cardNumber}
              onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
              disabled={isProcessing}
              className="font-mono"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="expiryDate">Expiry Date</Label>
              <Input
                id="expiryDate"
                placeholder="MM/YY"
                value={expiryDate}
                onChange={(e) => setExpiryDate(formatExpiryDate(e.target.value))}
                disabled={isProcessing}
                className="font-mono"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cvv">CVV</Label>
              <Input
                id="cvv"
                placeholder="123"
                value={cvv}
                onChange={(e) => setCvv(e.target.value.replace(/\D/g, '').substring(0, 3))}
                disabled={isProcessing}
                className="font-mono"
              />
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
            <Lock className="h-4 w-4" />
            <span>Your payment information is secure and encrypted</span>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isProcessing}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isProcessing}
              className="flex-1 gradient-primary text-primary-foreground"
            >
              {isProcessing ? "Processing..." : `Pay $${amount}`}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}