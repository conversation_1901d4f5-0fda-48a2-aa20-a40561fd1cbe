import { useAuth } from '@/contexts/AuthContext'
import { Navigate } from 'react-router-dom'

interface ProtectedRouteProps {
  children: React.ReactNode
  adminOnly?: boolean
  staffOnly?: boolean
}

export const ProtectedRoute = ({ children, adminOnly = false, staffOnly = false }: ProtectedRouteProps) => {
  const { user, userProfile, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return <Navigate to="/auth" replace />
  }

  if (adminOnly && userProfile?.role !== 'admin') {
    return <Navigate to="/" replace />
  }

  if (staffOnly && !['admin', 'staff'].includes(userProfile?.role || '')) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}