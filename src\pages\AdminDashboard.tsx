import { useState, useEffect } from 'react'
import { Head<PERSON> } from '@/components/Header'
import { StaffManagement } from '@/components/StaffManagement'
import { AnalyticsDashboard } from '@/components/AnalyticsDashboard'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { supabase } from '@/integrations/supabase/client'
import { User, Animal, AnimalUpdate, Sponsorship } from '@/types/database'
import { getBreedsBySpecies } from '@/data/breeds'
import { toast } from '@/hooks/use-toast'
import { Users, Heart, DollarSign, AlertCircle, Plus, Edit, Trash2, Upload, BarChart3 } from 'lucide-react'

export const AdminDashboard = () => {
  const [users, setUsers] = useState<User[]>([])
  const [animals, setAnimals] = useState<Animal[]>([])
  const [updates, setUpdates] = useState<AnimalUpdate[]>([])
  const [sponsorships, setSponsorships] = useState<Sponsorship[]>([])
  const [animalPhotos, setAnimalPhotos] = useState<File[]>([]);
  const [animalVideos, setAnimalVideos] = useState<File[]>([]);
  const [loading, setLoading] = useState(false)
  const [editingAnimal, setEditingAnimal] = useState<Animal | null>(null)
  const [isEditMode, setIsEditMode] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)

  const [deleteConfirm, setDeleteConfirm] = useState<{ show: boolean; animal: Animal | null }>({ show: false, animal: null })
  const [newAnimal, setNewAnimal] = useState<Partial<Animal>>({
    name: '',
    species: 'dog',
    breed: '',
    age: '',
    gender: 'unknown',
    size: 'medium',
    status: 'available',
    intake_date: new Date().toISOString().split('T')[0],
    rescue_location: '',
    intake_story: '',
    medical_notes: '',
    behavioral_notes: '',
    photos: [],
    videos: []
  })

  useEffect(() => {
    console.log("AdminDashboard mounted, calling fetchData");
    fetchData()
  }, [])

  const fetchData = async () => {
    console.log("fetchData started"); 
    try {
      setLoading(true)
      
      // Fetch users
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })
        
      if (usersError) throw usersError
      setUsers((usersData || []) as User[])

      // Fetch animals
      const { data: animalsData, error: animalsError } = await supabase
        .from('animals')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (animalsError) throw animalsError
      setAnimals((animalsData || []) as Animal[])

      // Fetch updates
      const { data: updatesData, error: updatesError } = await supabase
        .from('animal_updates')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50)
      
      if (updatesError) throw updatesError
      setUpdates((updatesData || []) as AnimalUpdate[])

      // Fetch sponsorships
      const { data: sponsorshipsData, error: sponsorshipsError } = await supabase
        .from('sponsorships')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (sponsorshipsError) {
        console.warn('Could not load sponsorships:', sponsorshipsError.message)
        setSponsorships([])
      } else {
        setSponsorships((sponsorshipsData || []) as Sponsorship[])
      }

    } catch (error: any) {
      toast({
        title: "Error loading data",
        description: error.message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setNewAnimal({
      name: '',
      species: 'dog',
      breed: '',
      age: '',
      gender: 'unknown',
      size: 'medium',
      status: 'available',
      intake_date: new Date().toISOString().split('T')[0],
      rescue_location: '',
      intake_story: '',
      medical_notes: '',
      behavioral_notes: '',
      photos: [],
      videos: []
    });
    setAnimalPhotos([]);
    setAnimalVideos([]);
    setEditingAnimal(null);
    setIsEditMode(false);
    setDialogOpen(false);
  };

  const handleEditAnimal = (animal: Animal) => {
    console.log('Edit button clicked for animal:', animal);
    setEditingAnimal(animal);
    setNewAnimal({
      name: animal.name,
      species: animal.species,
      breed: animal.breed || '',
      age: animal.age || '',
      gender: animal.gender,
      size: animal.size,
      status: animal.status,
      intake_date: animal.intake_date,
      rescue_location: animal.rescue_location || '',
      intake_story: animal.intake_story || '',
      medical_notes: animal.medical_notes || '',
      behavioral_notes: animal.behavioral_notes || '',
      photos: animal.photos || [],
      videos: animal.videos || []
    });
    setAnimalPhotos([]);
    setAnimalVideos([]);
    setIsEditMode(true);
    setDialogOpen(true);
    console.log('Dialog state set to open, edit mode enabled');
  };

  const handleUpdateAnimal = async () => {
    if (!editingAnimal) {
      console.error('No editing animal found');
      return;
    }

    try {
      setLoading(true);
      
      console.log('Updating animal with data:', {
        editingAnimal,
        newAnimal,
        animalPhotos,
        animalVideos
      });
      
      // Validate required fields
      if (!newAnimal.name || !newAnimal.species) {
        toast({
          title: "Missing required fields",
          description: "Please fill in at least the name and species.",
          variant: "destructive",
        });
        return;
      }

      let photoUrls = [...(editingAnimal.photos || [])];
      let videoUrls = [...(editingAnimal.videos || [])];

      // Upload new photos if any
      for (const file of animalPhotos) {
        const { data, error } = await supabase.storage
          .from('animal-media')
          .upload(`photos/${Date.now()}_${file.name}`, file);
        if (error) {
          toast({ title: "Photo upload failed", description: error.message, variant: "destructive" });
          return;
        }
        const url = supabase.storage.from('animal-media').getPublicUrl(data.path).data.publicUrl;
        photoUrls.push(url);
      }

      // Upload new videos if any
      for (const file of animalVideos) {
        const { data, error } = await supabase.storage
          .from('animal-media')
          .upload(`videos/${Date.now()}_${file.name}`, file);
        if (error) {
          toast({ title: "Video upload failed", description: error.message, variant: "destructive" });
          return;
        }
        const url = supabase.storage.from('animal-media').getPublicUrl(data.path).data.publicUrl;
        videoUrls.push(url);
      }

      // Prepare update data with fallbacks
      const updateData = {
        name: newAnimal.name || editingAnimal.name,
        species: newAnimal.species || editingAnimal.species,
        breed: newAnimal.breed || editingAnimal.breed || '',
        age: newAnimal.age || editingAnimal.age || '',
        gender: newAnimal.gender || editingAnimal.gender,
        size: newAnimal.size || editingAnimal.size,
        status: newAnimal.status || editingAnimal.status,
        intake_date: newAnimal.intake_date || editingAnimal.intake_date,
        rescue_location: newAnimal.rescue_location || editingAnimal.rescue_location || '',
        intake_story: newAnimal.intake_story || editingAnimal.intake_story || '',
        medical_notes: newAnimal.medical_notes || editingAnimal.medical_notes || '',
        behavioral_notes: newAnimal.behavioral_notes || editingAnimal.behavioral_notes || '',
        photos: photoUrls,
        videos: videoUrls,
        updated_at: new Date().toISOString()
      };

      console.log('Sending update data to database:', updateData);

      // Update animal in database
      const { error } = await supabase
        .from('animals')
        .update(updateData)
        .eq('id', editingAnimal.id);

      if (error) {
        console.error('Supabase update error:', error);
        throw error;
      }

      console.log('Animal updated successfully in database');

      toast({
        title: "Animal updated successfully",
        description: `${updateData.name} has been updated.`,
      });

      resetForm();
      await fetchData();
    } catch (error: any) {
      console.error('Error updating animal:', error);
      toast({
        title: "Error updating animal",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAnimal = async (animal: Animal) => {
    setDeleteConfirm({ show: true, animal: animal });
  };

  const confirmDeleteAnimal = async () => {
    if (!deleteConfirm.animal) return;

    try {
      setLoading(true);
      
      console.log('Starting delete process for animal:', deleteConfirm.animal);
      console.log('Animal ID:', deleteConfirm.animal.id);
      console.log('Animal name:', deleteConfirm.animal.name);
      
      // First, let's check if the animal exists and we can access it
      const { data: checkData, error: checkError } = await supabase
        .from('animals')
        .select('id, name')
        .eq('id', deleteConfirm.animal.id)
        .single();
      
      if (checkError) {
        console.error('Error checking animal existence:', checkError);
        toast({
          title: "Error",
          description: "Could not verify animal exists. Check permissions.",
          variant: "destructive",
        });
        return;
      }
      
      if (!checkData) {
        console.error('Animal not found in database');
        toast({
          title: "Error",
          description: "Animal not found in database.",
          variant: "destructive",
        });
        return;
      }
      
      console.log('Animal found in database, proceeding with deletion');
      
      // Delete animal from database
      const { error: deleteError } = await supabase
        .from('animals')
        .delete()
        .eq('id', deleteConfirm.animal.id);
      
      if (deleteError) {
        console.error('Database delete error:', deleteError);
        console.error('Error details:', {
          message: deleteError.message,
          details: deleteError.details,
          hint: deleteError.hint,
          code: deleteError.code
        });
        throw deleteError;
      }
      
      console.log('Animal deleted from database successfully');
      
      // Then try to delete media files from storage (optional cleanup)
      if (deleteConfirm.animal.photos && deleteConfirm.animal.photos.length > 0) {
        console.log('Attempting to delete photos:', deleteConfirm.animal.photos.length);
        for (const photoUrl of deleteConfirm.animal.photos) {
          try {
            // Extract the path from the full URL
            const urlParts = photoUrl.split('/');
            const path = urlParts.slice(-2).join('/'); // Get photos/filename or videos/filename
            console.log('Attempting to delete photo:', path);
            
            const { error: storageError } = await supabase.storage
              .from('animal-media')
              .remove([path]);
              
            if (storageError) {
              console.warn('Failed to delete photo from storage:', storageError);
            } else {
              console.log('Photo deleted from storage:', path);
            }
          } catch (error) {
            console.warn('Failed to delete photo:', error);
          }
        }
      }

      if (deleteConfirm.animal.videos && deleteConfirm.animal.videos.length > 0) {
        console.log('Attempting to delete videos:', deleteConfirm.animal.videos.length);
        for (const videoUrl of deleteConfirm.animal.videos) {
          try {
            // Extract the path from the full URL
            const urlParts = videoUrl.split('/');
            const path = urlParts.slice(-2).join('/'); // Get photos/filename or videos/filename
            console.log('Attempting to delete video:', path);
            
            const { error: storageError } = await supabase.storage
              .from('animal-media')
              .remove([path]);
              
            if (storageError) {
              console.warn('Failed to delete video from storage:', storageError);
            } else {
              console.log('Video deleted from storage:', path);
            }
          } catch (error) {
            console.warn('Failed to delete video:', error);
          }
        }
      }
      
      toast({
        title: "Animal removed",
        description: `${deleteConfirm.animal.name} has been removed from the system.`,
      })
      
      // Refresh the data
      console.log('Refreshing data after deletion');
      await fetchData();
      
    } catch (error: any) {
      console.error('Error removing animal:', error);
      toast({
        title: "Error removing animal",
        description: error.message || "Failed to remove animal from database",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      setDeleteConfirm({ show: false, animal: null });
    }
  }

  const removeMediaItem = (type: 'photo' | 'video', index: number) => {
    if (isEditMode && editingAnimal) {
      if (type === 'photo') {
        const newPhotos = [...(editingAnimal.photos || [])];
        newPhotos.splice(index, 1);
        setEditingAnimal({ ...editingAnimal, photos: newPhotos });
      } else {
        const newVideos = [...(editingAnimal.videos || [])];
        newVideos.splice(index, 1);
        setEditingAnimal({ ...editingAnimal, videos: newVideos });
      }
    }
  };

  const clearNewMedia = () => {
    setAnimalPhotos([]);
    setAnimalVideos([]);
  };

  const testDatabasePermissions = async () => {
    try {
      console.log('Testing database permissions...');
      
      // Test 1: Can we read animals?
      const { data: readData, error: readError } = await supabase
        .from('animals')
        .select('id, name')
        .limit(1);
      
      console.log('Read test result:', { data: readData, error: readError });
      
      // Test 2: Can we update an animal?
      if (animals.length > 0) {
        const testAnimal = animals[0];
        const { error: updateError } = await supabase
          .from('animals')
          .update({ updated_at: new Date().toISOString() })
          .eq('id', testAnimal.id);
        
        console.log('Update test result:', { error: updateError });
      }
      
      // Test 3: Can we delete an animal?
      if (animals.length > 0) {
        const testAnimal = animals[0];
        const { error: deleteError } = await supabase
          .from('animals')
          .delete()
          .eq('id', testAnimal.id);
        
        console.log('Delete test result:', { error: deleteError });
        
        // If delete succeeded, we need to re-add the animal
        if (!deleteError) {
          console.log('Delete test succeeded, re-adding animal...');
          await fetchData();
        }
      }
      
      toast({
        title: "Permission test completed",
        description: "Check console for results",
      });
      
    } catch (error: any) {
      console.error('Permission test error:', error);
      toast({
        title: "Permission test failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleCreateAnimal = async () => {
    try {
      // Validate required fields
      if (!newAnimal.name || !newAnimal.species) {
        toast({
          title: "Missing required fields",
          description: "Please fill in at least the name and species.",
          variant: "destructive",
        });
        return;
      }

      // 1. Upload photos
      let photoUrls = [];
      for (const file of animalPhotos) {
        const { data, error } = await supabase.storage
          .from('animal-media')
          .upload(`photos/${Date.now()}_${file.name}`, file);
        if (error) {
          toast({ title: "Photo upload failed", description: error.message, variant: "destructive" });
          return;
        }
        const url = supabase.storage.from('animal-media').getPublicUrl(data.path).data.publicUrl;
        photoUrls.push(url);
      }

      // 2. Upload videos
      let videoUrls = [];
      for (const file of animalVideos) {
        const { data, error } = await supabase.storage
          .from('animal-media')
          .upload(`videos/${Date.now()}_${file.name}`, file);
        if (error) {
          toast({ title: "Video upload failed", description: error.message, variant: "destructive" });
          return;
        }
        const url = supabase.storage.from('animal-media').getPublicUrl(data.path).data.publicUrl;
        videoUrls.push(url);
      }

      // 3. Insert animal with photo/video URLs
      const animalData = {
        name: newAnimal.name!,
        species: newAnimal.species!,
        breed: newAnimal.breed || '',
        age: newAnimal.age || '',
        gender: newAnimal.gender || 'unknown',
        size: newAnimal.size || 'medium',
        status: newAnimal.status || 'available',
        intake_date: newAnimal.intake_date || new Date().toISOString().split('T')[0],
        rescue_location: newAnimal.rescue_location || '',
        intake_story: newAnimal.intake_story || '',
        medical_notes: newAnimal.medical_notes || '',
        behavioral_notes: newAnimal.behavioral_notes || '',
        photos: photoUrls,
        videos: videoUrls,
      };

      const { error } = await supabase
        .from('animals')
        .insert([animalData]);

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      toast({
        title: "Animal added successfully",
        description: `${newAnimal.name} has been added to the system.`,
      });

      resetForm();
      await fetchData();
    } catch (error: any) {
      console.error('Error adding animal:', error);
      toast({
        title: "Error adding animal",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'staff': return 'bg-blue-100 text-blue-800'
      case 'volunteer': return 'bg-green-100 text-green-800'
      case 'foster': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'adopted': return 'bg-blue-100 text-blue-800'
      case 'fostered': return 'bg-purple-100 text-purple-800'
      case 'medical_hold': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  const totalDonations = sponsorships.reduce((sum, s) => sum + s.amount, 0)
  const activeAnimals = animals.filter(a => ['available', 'fostered'].includes(a.status)).length
  const adoptedAnimals = animals.filter(a => a.status === 'adopted').length

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold gradient-text">Admin Dashboard</h1>
            <p className="text-muted-foreground">Manage users, animals, and rescue operations</p>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{users.length}</p>
                </div>
                <Users className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Animals</p>
                  <p className="text-2xl font-bold">{activeAnimals}</p>
                </div>
                <Heart className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Successfully Adopted</p>
                  <p className="text-2xl font-bold">{adoptedAnimals}</p>
                </div>
                <Heart className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Donations</p>
                  <p className="text-2xl font-bold">${totalDonations.toLocaleString()}</p>
                </div>
                <DollarSign className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="analytics" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="animals">Animals</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="updates">Recent Updates</TabsTrigger>
            <TabsTrigger value="sponsorships">Sponsorships</TabsTrigger>
          </TabsList>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <AnalyticsDashboard />
          </TabsContent>

          {/* Animals Tab */}
          <TabsContent value="animals" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-semibold">Animal Management</h2>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={testDatabasePermissions}
                  className="text-sm"
                >
                  Test Permissions
                </Button>
                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="gradient-primary">
                      <Plus className="h-4 w-4 mr-2" />
                      Add New Animal
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="flex items-center justify-between">
                        {isEditMode ? 'Edit Animal' : 'Add New Animal'}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDialogOpen(false)}
                          className="h-6 w-6 p-0"
                        >
                          ×
                        </Button>
                      </DialogTitle>
                    </DialogHeader>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Name</Label>
                        <Input
                          id="name"
                          value={newAnimal.name}
                          onChange={(e) => setNewAnimal({...newAnimal, name: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="species">Species</Label>
                        <Select 
                          value={newAnimal.species} 
                          onValueChange={(value) => setNewAnimal({...newAnimal, species: value as 'dog' | 'cat' | 'other'})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="dog">Dog</SelectItem>
                            <SelectItem value="cat">Cat</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="breed">Breed</Label>
                        <Select 
                          value={newAnimal.breed || ''} 
                          onValueChange={(value) => setNewAnimal({...newAnimal, breed: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select breed" />
                          </SelectTrigger>
                          <SelectContent>
                            {getBreedsBySpecies(newAnimal.species || 'dog').map((breed) => (
                              <SelectItem key={breed} value={breed}>
                                {breed}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="age">Age</Label>
                        <Input
                          id="age"
                          value={newAnimal.age}
                          onChange={(e) => setNewAnimal({...newAnimal, age: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="gender">Gender</Label>
                        <Select 
                          value={newAnimal.gender} 
                          onValueChange={(value) => setNewAnimal({...newAnimal, gender: value as 'male' | 'female' | 'unknown'})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="unknown">Unknown</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="size">Size</Label>
                        <Select 
                          value={newAnimal.size} 
                          onValueChange={(value) => setNewAnimal({...newAnimal, size: value as 'small' | 'medium' | 'large'})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="small">Small</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="large">Large</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="status">Status</Label>
                        <Select 
                          value={newAnimal.status} 
                          onValueChange={(value) => setNewAnimal({...newAnimal, status: value as 'available' | 'pending' | 'adopted' | 'fostered' | 'medical_hold'})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="available">Available</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="adopted">Adopted</SelectItem>
                            <SelectItem value="fostered">Fostered</SelectItem>
                            <SelectItem value="medical_hold">Medical Hold</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="intake_date">Intake Date</Label>
                        <Input
                          id="intake_date"
                          type="date"
                          value={newAnimal.intake_date}
                          onChange={(e) => setNewAnimal({...newAnimal, intake_date: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="rescue_location">Rescue Location</Label>
                        <Input
                          id="rescue_location"
                          value={newAnimal.rescue_location}
                          onChange={(e) => setNewAnimal({...newAnimal, rescue_location: e.target.value})}
                        />
                      </div>
                      <div className="col-span-2">
                        <Label htmlFor="intake_story">Intake Story</Label>
                        <Textarea
                          id="intake_story"
                          value={newAnimal.intake_story}
                          onChange={(e) => setNewAnimal({...newAnimal, intake_story: e.target.value})}
                          rows={3}
                        />
                      </div>
                      <div className="col-span-2">
                        <Label htmlFor="medical_notes">Medical Notes</Label>
                        <Textarea
                          id="medical_notes"
                          value={newAnimal.medical_notes}
                          onChange={(e) => setNewAnimal({...newAnimal, medical_notes: e.target.value})}
                          rows={2}
                        />
                      </div>
                      <div className="col-span-2">
                        <Label htmlFor="behavioral_notes">Behavioral Notes</Label>
                        <Textarea
                          id="behavioral_notes"
                          value={newAnimal.behavioral_notes}
                          onChange={(e) => setNewAnimal({...newAnimal, behavioral_notes: e.target.value})}
                          rows={2}
                        />
                      </div>

                      {/* Existing Media Display (Edit Mode) */}
                      {isEditMode && editingAnimal && (
                        <>
                          {editingAnimal.photos && editingAnimal.photos.length > 0 && (
                            <div className="col-span-2">
                              <Label>Existing Photos</Label>
                              <div className="grid grid-cols-4 gap-2 mt-2">
                                {editingAnimal.photos.map((photo, index) => (
                                  <div key={index} className="relative group">
                                    <img 
                                      src={photo} 
                                      alt={`Photo ${index + 1}`}
                                      className="w-full h-20 object-cover rounded border"
                                    />
                                    <button
                                      onClick={() => removeMediaItem('photo', index)}
                                      className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                      ×
                                    </button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {editingAnimal.videos && editingAnimal.videos.length > 0 && (
                            <div className="col-span-2">
                              <Label>Existing Videos</Label>
                              <div className="grid grid-cols-4 gap-2 mt-2">
                                {editingAnimal.videos.map((video, index) => (
                                  <div key={index} className="relative group">
                                    <video 
                                      src={video}
                                      className="w-full h-20 object-cover rounded border"
                                      muted
                                    />
                                    <button
                                      onClick={() => removeMediaItem('video', index)}
                                      className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                      ×
                                    </button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </>
                      )}

                      {/* New Media Upload */}
                      <div className="col-span-2">
                        <Label htmlFor="animal-photos">Add New Photos</Label>
                        <Input
                          id="animal-photos"
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={e => setAnimalPhotos(Array.from(e.target.files || []))}
                        />
                        <div className="flex flex-wrap gap-2 mt-2">
                          {animalPhotos.map((file, idx) => (
                            <span key={idx} className="text-xs bg-muted px-2 py-1 rounded">{file.name}</span>
                          ))}
                        </div>
                        {animalPhotos.length > 0 && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={clearNewMedia}
                            className="mt-2"
                          >
                            Clear New Media
                          </Button>
                        )}
                      </div>
                      <div className="col-span-2">
                        <Label htmlFor="animal-videos">Add New Videos</Label>
                        <Input
                          id="animal-videos"
                          type="file"
                          accept="video/*"
                          multiple
                          onChange={e => setAnimalVideos(Array.from(e.target.files || []))}
                        />
                        <div className="flex flex-wrap gap-2 mt-2">
                          {animalVideos.map((file, idx) => (
                            <span key={idx} className="text-xs bg-muted px-2 py-1 rounded">{file.name}</span>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      {isEditMode && (
                        <Button 
                          variant="outline" 
                          onClick={resetForm}
                          className="flex-1"
                          disabled={loading}
                        >
                          Cancel
                        </Button>
                      )}
                      <Button 
                        onClick={isEditMode ? handleUpdateAnimal : handleCreateAnimal} 
                        className={`${isEditMode ? 'flex-1' : 'w-full'} gradient-primary`}
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            {isEditMode ? 'Updating...' : 'Adding...'}
                          </>
                        ) : (
                          isEditMode ? 'Update Animal' : 'Add Animal'
                        )}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <div className="grid gap-4">
              {animals.map((animal) => (
                <Card key={animal.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-4 flex-1">
                        {/* Animal Image */}
                        <div className="flex-shrink-0">
                          {animal.photos && animal.photos.length > 0 ? (
                            <img
                              src={animal.photos[0]}
                              alt={animal.name}
                              className="w-16 h-16 rounded-lg object-cover border border-gray-200"
                              onError={(e) => {
                                // Fallback to a placeholder if image fails to load
                                e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyMCAyNC42ODYzIDIyLjY4NjMgMjIgMjYgMjJIMzguNUM0MS44MTM3IDIyIDQ0LjUgMjQuNjg2MyA0NC41IDI4VjM2QzQ0LjUgMzkuMzEzNyA0MS44MTM3IDQyIDM4LjUgNDJIMjZDMjIuNjg2MyA0MiAyMCAzOS4zMTM3IDIwIDM2VjI4WiIgZmlsbD0iI0Q5RDlEOSIvPgo8Y2lyY2xlIGN4PSIyOCIgY3k9IjI4IiByPSIyIiBmaWxsPSIjOTk5OTk5Ii8+CjxwYXRoIGQ9Ik0yMiAzNkwyNiAzMkwzMCAzNkwzNCAzMkwzOCAzNlY0MEgyMlYzNloiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+';
                              }}
                            />
                          ) : (
                            // Placeholder when no image is available
                            <div className="w-16 h-16 rounded-lg bg-gray-100 border border-gray-200 flex items-center justify-center">
                              <Heart className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Animal Details */}
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold">{animal.name}</h3>
                            <Badge className={getStatusColor(animal.status)}>
                              {animal.status.replace('_', ' ')}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-muted-foreground">
                            <span>{animal.species} • {animal.breed}</span>
                            <span>{animal.age} • {animal.gender}</span>
                            <span>Size: {animal.size}</span>
                            <span>Intake: {new Date(animal.intake_date).toLocaleDateString()}</span>
                          </div>
                          {animal.rescue_location && (
                            <p className="text-sm text-muted-foreground mt-1">
                              Rescued from: {animal.rescue_location}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleEditAnimal(animal)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteAnimal(animal)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
            <StaffManagement />
          </TabsContent>

          {/* Updates Tab */}
          <TabsContent value="updates" className="space-y-6">
            <h2 className="text-2xl font-semibold">Recent Updates</h2>
            <div className="grid gap-4">
              {updates.map((update) => (
                <Card key={update.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <Badge>{update.update_type}</Badge>
                        <span className="text-sm text-muted-foreground">
                          by {update.author_name} ({update.author_role})
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(update.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-foreground">{update.content}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Sponsorships Tab */}
          <TabsContent value="sponsorships" className="space-y-6">
            <h2 className="text-2xl font-semibold">Sponsorship Management</h2>
            <div className="grid gap-4">
              {sponsorships.map((sponsorship) => (
                <Card key={sponsorship.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold capitalize">
                            {sponsorship.sponsor_type.replace('_', ' ')} Sponsorship
                          </h3>
                          <Badge className={sponsorship.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}>
                            {sponsorship.status}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground">
                          ${sponsorship.amount} of ${sponsorship.target_amount} raised
                        </p>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${(sponsorship.amount / sponsorship.target_amount) * 100}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          Started: {new Date(sponsorship.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirm.show} onOpenChange={(open) => !open && setDeleteConfirm({ show: false, animal: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-foreground">
              Are you sure you want to delete <strong>{deleteConfirm.animal?.name}</strong>? 
              This action cannot be undone and will permanently remove:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>The animal's profile and all information</li>
              <li>All associated photos and videos</li>
              <li>Any updates and records</li>
            </ul>
            <div className="flex gap-2 justify-end">
              <Button 
                variant="outline" 
                onClick={() => setDeleteConfirm({ show: false, animal: null })}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={confirmDeleteAnimal}
                disabled={loading}
              >
                {loading ? 'Deleting...' : 'Delete Permanently'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AdminDashboard
